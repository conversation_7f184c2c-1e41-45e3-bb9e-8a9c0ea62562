#!/usr/bin/env python3
"""
测试客户端示例
演示如何调用签名生成API
"""

import requests
import json

# 服务地址
BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"请求失败: {e}")
    print()

def test_generate_signature():
    """测试签名生成接口"""
    print("=== 测试签名生成接口 ===")
    
    # 测试数据
    test_data = {
        "method": "GET",
        "uri": "/api/sns/web/v1/user_posted",
        "a1_value": "test_a1_cookie_value",
        "payload": {
            "num": "30",
            "cursor": "",
            "user_id": "123"
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/generate-signature",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"请求失败: {e}")
    print()

def test_api_info():
    """测试API信息接口"""
    print("=== 测试API信息接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/api-info")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"请求失败: {e}")
    print()

def test_invalid_method():
    """测试无效的HTTP方法"""
    print("=== 测试无效的HTTP方法 ===")
    
    test_data = {
        "method": "DELETE",  # 无效方法
        "uri": "/api/sns/web/v1/user_posted",
        "a1_value": "test_a1_cookie_value",
        "payload": {}
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/generate-signature",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"请求失败: {e}")
    print()

if __name__ == "__main__":
    print("小红书签名生成服务测试客户端")
    print("请确保服务已启动 (python start.py)")
    print("=" * 50)
    
    # 运行测试
    test_health()
    test_api_info()
    test_generate_signature()
    test_invalid_method()
    
    print("测试完成！")
    print("\n使用说明:")
    print("1. 启动服务: python start.py")
    print("2. 访问API文档: http://localhost:8000/docs")
    print("3. 健康检查: http://localhost:8000/health")
    print("4. 运行测试: python test_client.py")
