# -*- coding: utf-8 -*-
"""
正确使用小红书签名生成服务的示例
"""

import json
import urllib.request

def generate_xiaohongshu_signature():
    """生成小红书签名的正确示例"""
    
    # 正确的请求格式
    request_data = {
        "method": "GET",
        "uri": "/api/solar/kol/data_v2/notes_detail",
        "a1_value": "196fca1b06btx3ck2bgkh6u4433khsrfjvgycbd0d30000295085",  # 只需要a1的值部分
        "payload": {
            "advertiseSwitch": 1,
            "orderType": 1,
            "pageNumber": 1,
            "pageSize": 8,
            "userId": "5ac63d1d4eacab4a4af08e12",
            "noteType": 4,
            "isThirdPlatform": 0
        }
    }
    
    try:
        # 发送请求到签名生成服务
        data_bytes = json.dumps(request_data).encode('utf-8')
        req = urllib.request.Request(
            "http://localhost:8000/generate-signature",
            data=data_bytes
        )
        req.add_header('Content-Type', 'application/json')
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
        
        if result['success']:
            signature = result['signature']
            print(f"✅ 签名生成成功!")
            print(f"签名: {signature}")
            
            # 现在你可以使用这个签名来调用小红书API
            print(f"\n📝 使用签名调用小红书API:")
            print(f"URL: https://pgy.xiaohongshu.com{request_data['uri']}")
            print(f"Headers中需要添加:")
            print(f"  X-S: {signature}")
            print(f"  X-T: {int(time.time() * 1000)}")  # 时间戳
            
            return signature
        else:
            print(f"❌ 签名生成失败: {result['error']}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

if __name__ == "__main__":
    import time
    
    print("小红书签名生成示例")
    print("=" * 40)
    
    signature = generate_xiaohongshu_signature()
    
    if signature:
        print(f"\n🎉 成功生成签名，可以用于调用小红书API了！")
    else:
        print(f"\n❌ 签名生成失败，请检查参数")
