# -*- coding: utf-8 -*-
"""
测试带时间戳的签名生成服务
"""

import json
import urllib.request
import time

# 服务地址
BASE_URL = "http://localhost:8000"

def test_signature_with_timestamp():
    """测试签名生成是否返回时间戳"""
    print("=== 测试签名生成（包含时间戳） ===")
    
    test_data = {
        "method": "GET",
        "uri": "/api/solar/kol/data_v2/notes_detail",
        "a1_value": "196fca1b06btx3ck2bgkh6u4433khsrfjvgycbd0d30000295085",
        "payload": {
            "advertiseSwitch": 1,
            "orderType": 1,
            "pageNumber": 1,
            "pageSize": 8,
            "userId": "5ac63d1d4eacab4a4af08e12",
            "noteType": 4,
            "isThirdPlatform": 0
        }
    }
    
    try:
        # 记录请求时间
        request_time = int(time.time() * 1000)
        
        # 发送请求
        data_bytes = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(f"{BASE_URL}/generate-signature", data=data_bytes)
        req.add_header('Content-Type', 'application/json')
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
        
        print(f"状态码: {response.status}")
        print(f"请求时间: {request_time}")
        
        if result['success']:
            print(f"✅ 签名生成成功!")
            print(f"签名: {result['signature']}")
            print(f"时间戳: {result['timestamp']}")
            print(f"X-T值: {result['x_t']}")
            
            # 验证时间戳的合理性
            time_diff = abs(result['timestamp'] - request_time)
            print(f"时间差: {time_diff}ms")
            
            if time_diff < 5000:  # 5秒内
                print(f"✅ 时间戳合理")
            else:
                print(f"⚠️ 时间戳可能有问题")
            
            # 显示如何使用
            print(f"\n📝 使用方法:")
            print(f"在调用小红书API时，请求头中添加:")
            print(f"  X-S: {result['signature']}")
            print(f"  X-T: {result['x_t']}")
            
            return result
        else:
            print(f"❌ 签名生成失败: {result['error']}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_multiple_requests():
    """测试多次请求的时间戳是否不同"""
    print("\n=== 测试多次请求时间戳差异 ===")
    
    test_data = {
        "method": "GET",
        "uri": "/api/sns/web/v1/user_posted",
        "a1_value": "test_a1_value",
        "payload": {"num": "10"}
    }
    
    results = []
    
    for i in range(3):
        try:
            data_bytes = json.dumps(test_data).encode('utf-8')
            req = urllib.request.Request(f"{BASE_URL}/generate-signature", data=data_bytes)
            req.add_header('Content-Type', 'application/json')
            
            with urllib.request.urlopen(req) as response:
                result = json.loads(response.read().decode('utf-8'))
            
            if result['success']:
                results.append({
                    'request': i + 1,
                    'timestamp': result['timestamp'],
                    'signature': result['signature'][:50] + '...'  # 只显示前50个字符
                })
                print(f"请求 {i+1}: 时间戳={result['timestamp']}, 签名={result['signature'][:30]}...")
            
            # 稍微延迟以确保时间戳不同
            time.sleep(0.1)
            
        except Exception as e:
            print(f"请求 {i+1} 失败: {e}")
    
    # 分析结果
    if len(results) >= 2:
        print(f"\n分析:")
        for i in range(1, len(results)):
            time_diff = results[i]['timestamp'] - results[i-1]['timestamp']
            sig_diff = results[i]['signature'] != results[i-1]['signature']
            print(f"请求{i}与请求{i+1}: 时间差={time_diff}ms, 签名不同={sig_diff}")

def test_api_info_updated():
    """测试API信息是否已更新"""
    print("\n=== 测试API信息更新 ===")
    
    try:
        req = urllib.request.Request(f"{BASE_URL}/api-info")
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
        
        print(f"状态码: {response.status}")
        
        if 'usage' in result and 'response' in result['usage']:
            print(f"✅ API信息已更新，包含响应格式说明")
            response_format = result['usage']['response']
            print(f"响应格式: {json.dumps(response_format, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️ API信息可能未完全更新")
            
    except Exception as e:
        print(f"❌ 获取API信息失败: {e}")

if __name__ == "__main__":
    print("测试带时间戳的小红书签名生成服务")
    print("=" * 50)
    
    # 运行测试
    result = test_signature_with_timestamp()
    test_multiple_requests()
    test_api_info_updated()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    
    if result:
        print(f"\n💡 重要提示:")
        print(f"1. 服务现在同时返回签名和时间戳")
        print(f"2. X-T时间戳对于小红书API调用很重要")
        print(f"3. 每次请求都会生成新的时间戳")
        print(f"4. 建议在调用小红书API时使用返回的时间戳")
