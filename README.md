# 小红书签名生成Web服务

基于xhshow库的小红书API签名参数生成Web服务，提供简单易用的HTTP API接口。

## 功能特性

- 🚀 基于FastAPI框架，性能优异
- 📝 自动生成API文档
- 🔒 完整的参数验证和错误处理
- 🌐 支持CORS跨域请求
- 📊 健康检查接口
- 🧪 包含完整的测试示例

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python start.py
```

服务将在 `http://localhost:8000` 启动

### 3. 访问API文档

打开浏览器访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API接口

### 生成签名

**POST** `/generate-signature`

生成小红书API签名参数。

#### 请求参数

```json
{
    "method": "GET",
    "uri": "/api/sns/web/v1/user_posted",
    "a1_value": "your_a1_cookie_value",
    "payload": {
        "num": "30",
        "cursor": "",
        "user_id": "123"
    }
}
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| method | string | 是 | HTTP方法，支持GET或POST |
| uri | string | 是 | API路径，必须以/开头 |
| a1_value | string | 是 | a1 cookie值 |
| payload | object | 否 | 请求参数，JSON格式，默认为空对象 |

#### 响应示例

成功响应：
```json
{
    "success": true,
    "signature": "generated_signature_string",
    "error": null
}
```

失败响应：
```json
{
    "success": false,
    "signature": null,
    "error": "错误信息"
}
```

### 健康检查

**GET** `/health`

检查服务运行状态。

#### 响应示例

```json
{
    "status": "healthy",
    "message": "服务运行正常"
}
```

### API信息

**GET** `/api-info`

获取API使用说明和示例。

## 使用示例

### Python示例

```python
import requests
import json

# 请求数据
data = {
    "method": "GET",
    "uri": "/api/sns/web/v1/user_posted",
    "a1_value": "your_a1_cookie_value",
    "payload": {
        "num": "30",
        "cursor": "",
        "user_id": "123"
    }
}

# 发送请求
response = requests.post(
    "http://localhost:8000/generate-signature",
    json=data
)

# 处理响应
result = response.json()
if result["success"]:
    print(f"签名: {result['signature']}")
else:
    print(f"错误: {result['error']}")
```

### curl示例

```bash
curl -X POST "http://localhost:8000/generate-signature" \
     -H "Content-Type: application/json" \
     -d '{
       "method": "GET",
       "uri": "/api/sns/web/v1/user_posted",
       "a1_value": "your_a1_cookie_value",
       "payload": {
         "num": "30",
         "cursor": "",
         "user_id": "123"
       }
     }'
```

## 测试

运行测试客户端：

```bash
python test_client.py
```

测试客户端会自动测试所有API接口，包括正常情况和异常情况。

## 项目结构

```
.
├── main.py              # 主服务文件
├── start.py             # 启动脚本
├── test_client.py       # 测试客户端
├── requirements.txt     # 依赖文件
└── README.md           # 说明文档
```

## 注意事项

1. 确保已安装xhshow库
2. a1_value参数不能为空
3. method参数只支持GET和POST
4. uri参数必须以/开头
5. 服务默认运行在8000端口

## 错误处理

服务包含完整的错误处理机制：

- 参数验证错误：返回422状态码和详细错误信息
- 签名生成失败：返回200状态码但success为false
- 服务不可用：健康检查返回503状态码

## 开发模式

启动开发模式（自动重载）：

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 部署建议

生产环境部署建议：

1. 使用Gunicorn或uWSGI作为WSGI服务器
2. 配置Nginx作为反向代理
3. 设置适当的日志级别
4. 配置监控和健康检查

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```
