# -*- coding: utf-8 -*-
"""
小红书加密参数获取Web服务
基于xhshow库提供签名生成API接口
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, Union
import json
import logging
from xhshow import Xhshow

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="小红书签名生成服务",
    description="提供小红书API签名参数生成的Web服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化xhshow客户端
xhshow_client = Xhshow()

class SignatureRequest(BaseModel):
    """签名请求模型"""
    method: str = Field(..., description="HTTP方法，支持GET或POST")
    uri: str = Field(..., description="API路径，如'/api/sns/web/v1/user_posted'")
    a1_value: str = Field(..., description="a1 cookie值")
    payload: Optional[Dict[str, Any]] = Field(default={}, description="请求参数，JSON格式")
    
    @validator('method')
    def validate_method(cls, v):
        if v.upper() not in ['GET', 'POST']:
            raise ValueError('method必须是GET或POST')
        return v.upper()
    
    @validator('uri')
    def validate_uri(cls, v):
        if not v.startswith('/'):
            raise ValueError('uri必须以/开头')
        return v
    
    @validator('a1_value')
    def validate_a1_value(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('a1_value不能为空')
        return v.strip()

class SignatureResponse(BaseModel):
    """签名响应模型"""
    success: bool = Field(..., description="是否成功")
    signature: Optional[str] = Field(None, description="生成的签名")
    error: Optional[str] = Field(None, description="错误信息")

class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    message: str = Field(..., description="状态信息")

@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径，返回服务信息"""
    return {
        "service": "小红书签名生成服务",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    try:
        # 简单测试xhshow客户端是否正常
        test_client = Xhshow()
        return HealthResponse(
            status="healthy",
            message="服务运行正常"
        )
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="服务不可用"
        )

@app.post("/generate-signature", response_model=SignatureResponse)
async def generate_signature(request: SignatureRequest):
    """
    生成小红书API签名
    
    Args:
        request: 签名请求参数
        
    Returns:
        SignatureResponse: 包含签名结果的响应
        
    Example:
        ```json
        {
            "method": "GET",
            "uri": "/api/sns/web/v1/user_posted",
            "a1_value": "your_a1_cookie_value",
            "payload": {"num": "30", "cursor": "", "user_id": "123"}
        }
        ```
    """
    try:
        logger.info(f"收到签名请求: method={request.method}, uri={request.uri}")
        
        # 调用xhshow生成签名
        signature = xhshow_client.sign_xs(
            method=request.method,
            uri=request.uri,
            a1_value=request.a1_value,
            payload=request.payload
        )
        
        logger.info("签名生成成功")
        return SignatureResponse(
            success=True,
            signature=signature
        )
        
    except Exception as e:
        error_msg = f"签名生成失败: {str(e)}"
        logger.error(error_msg)
        return SignatureResponse(
            success=False,
            error=error_msg
        )

@app.get("/api-info")
async def get_api_info():
    """获取API使用说明"""
    return {
        "title": "小红书签名生成服务API说明",
        "endpoints": {
            "POST /generate-signature": {
                "description": "生成小红书API签名",
                "parameters": {
                    "method": "HTTP方法（GET或POST）",
                    "uri": "API路径，如'/api/sns/web/v1/user_posted'",
                    "a1_value": "a1 cookie值",
                    "payload": "请求参数（JSON格式，可选）"
                },
                "example": {
                    "method": "GET",
                    "uri": "/api/sns/web/v1/user_posted",
                    "a1_value": "your_a1_cookie_value",
                    "payload": {"num": "30", "cursor": "", "user_id": "123"}
                }
            },
            "GET /health": {
                "description": "健康检查接口"
            },
            "GET /api-info": {
                "description": "获取API使用说明"
            }
        },
        "usage_notes": [
            "所有请求都应该使用JSON格式",
            "method参数只支持GET和POST",
            "uri参数必须以/开头",
            "a1_value不能为空",
            "payload参数是可选的，默认为空字典"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
