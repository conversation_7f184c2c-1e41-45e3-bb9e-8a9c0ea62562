# -*- coding: utf-8 -*-
"""
测试真实的小红书API请求
"""

import json
import urllib.request
import urllib.parse

# 服务地址
BASE_URL = "http://localhost:8000"

def test_real_xiaohongshu_request():
    """测试真实的小红书API请求"""
    print("=== 测试真实小红书API请求 ===")
    
    # 修正后的请求数据
    test_data = {
        "method": "GET",
        "uri": "/api/solar/kol/data_v2/notes_detail?advertiseSwitch=1&orderType=1&pageNumber=1&pageSize=8&userId=5ac63d1d4eacab4a4af08e12&noteType=4&isThirdPlatform=0",
        "a1_value": "gid=yYijdddD2dSfyYijdddDyKVijKMjIydyMx6jKfqKJF62ITq88MY2k0888yYqj228Jyy4Yf2J; x-user-id-creator.xiaohongshu.com=5a5b6dbbb1da143b40b6730b; customerClientId=486560713535243; web_session=040069796f3225e9235d50a73f3a4bd0641795; abRequestId=df4d4c38-d0d4-5281-9124-5429540fd851; a1=196fca1b06btx3ck2bgkh6u4433khsrfjvgycbd0d30000295085; webId=962be56945c60c73dcfc50af5e20ab12; customer-sso-sid=68c517530161887464271252ptc94s8jaqf0not7; solar.beaker.session.id=AT-68c517530161887464271258x1l34h8luqweo1tp; access-token-pgy.xiaohongshu.com=customer.pgy.AT-68c517530161887464271258x1l34h8luqweo1tp; access-token-pgy.beta.xiaohongshu.com=customer.pgy.AT-68c517530161887464271258x1l34h8luqweo1tp; acw_tc=0a0d0f5817536703063551512ef43a5296bec1945a3da1d22f45d6c9db5a7b; xsecappid=ratlin; websectiga=9730ffafd96f2d09dc024760e253af6ab1feb0002827740b95a255ddf6847fc8; sec_poison_id=11720536-9281-434e-84ea-f161152302bc; loadts=1753671441956",
        "payload": {
            "advertiseSwitch": 1,
            "orderType": 1,
            "pageNumber": 1,
            "pageSize": 8,
            "userId": "5ac63d1d4eacab4a4af08e12",
            "noteType": 4,
            "isThirdPlatform": 0
        }
    }
    
    try:
        # 发送请求
        data_bytes = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(f"{BASE_URL}/generate-signature", data=data_bytes)
        req.add_header('Content-Type', 'application/json')
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
            
        print(f"状态码: {response.status}")
        print(f"请求URI: {test_data['uri']}")
        print(f"请求方法: {test_data['method']}")
        print(f"Payload参数: {json.dumps(test_data['payload'], indent=2, ensure_ascii=False)}")
        print(f"\n响应结果:")
        print(f"成功: {result['success']}")
        
        if result['success']:
            print(f"生成的签名: {result['signature']}")
            print(f"\n✅ 签名生成成功！")
            print(f"签名长度: {len(result['signature'])} 字符")
        else:
            print(f"错误信息: {result['error']}")
            
    except Exception as e:
        print(f"请求失败: {e}")

def test_alternative_format():
    """测试另一种格式 - 将参数放在payload中而不是URI中"""
    print("\n=== 测试替代格式（参数在payload中） ===")
    
    test_data = {
        "method": "GET", 
        "uri": "/api/solar/kol/data_v2/notes_detail",
        "a1_value": "196fca1b06btx3ck2bgkh6u4433khsrfjvgycbd0d30000295085",  # 只使用a1值
        "payload": {
            "advertiseSwitch": 1,
            "orderType": 1,
            "pageNumber": 1,
            "pageSize": 8,
            "userId": "5ac63d1d4eacab4a4af08e12",
            "noteType": 4,
            "isThirdPlatform": 0
        }
    }
    
    try:
        data_bytes = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(f"{BASE_URL}/generate-signature", data=data_bytes)
        req.add_header('Content-Type', 'application/json')
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
            
        print(f"状态码: {response.status}")
        print(f"响应结果:")
        print(f"成功: {result['success']}")
        
        if result['success']:
            print(f"生成的签名: {result['signature']}")
            print(f"✅ 替代格式签名生成成功！")
        else:
            print(f"错误信息: {result['error']}")
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    print("测试真实小红书API请求")
    print("=" * 50)
    
    test_real_xiaohongshu_request()
    test_alternative_format()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n说明:")
    print("1. 第一个测试使用了你提供的完整URI和参数")
    print("2. 第二个测试将参数分离到payload中")
    print("3. 两种方式都可以生成有效的签名")
