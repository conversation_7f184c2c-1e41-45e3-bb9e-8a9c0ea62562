# -*- coding: utf-8 -*-
"""
简化版小红书签名生成服务
使用Python内置的http.server，不依赖外部库
"""

import json
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from xhshow import Xhshow

# 初始化xhshow客户端
xhshow_client = Xhshow()

class SignatureHandler(BaseHTTPRequestHandler):
    def _set_headers(self, status_code=200):
        """设置响应头"""
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_OPTIONS(self):
        """处理预检请求"""
        self._set_headers()

    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self._set_headers()
            response = {
                "service": "小红书签名生成服务",
                "version": "1.0.0",
                "endpoints": {
                    "POST /generate-signature": "生成签名",
                    "GET /health": "健康检查",
                    "GET /api-info": "API信息"
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        elif self.path == '/health':
            self._set_headers()
            response = {
                "status": "healthy",
                "message": "服务运行正常"
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        elif self.path == '/api-info':
            self._set_headers()
            response = {
                "title": "小红书签名生成服务API说明",
                "usage": {
                    "endpoint": "POST /generate-signature",
                    "parameters": {
                        "method": "HTTP方法（GET或POST）",
                        "uri": "API路径",
                        "a1_value": "a1 cookie值",
                        "payload": "请求参数（JSON格式）"
                    },
                    "example": {
                        "method": "GET",
                        "uri": "/api/sns/web/v1/user_posted",
                        "a1_value": "your_a1_cookie_value",
                        "payload": {"num": "30", "cursor": "", "user_id": "123"}
                    }
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        else:
            self._set_headers(404)
            response = {"error": "Not Found"}
            self.wfile.write(json.dumps(response).encode('utf-8'))

    def do_POST(self):
        """处理POST请求"""
        if self.path == '/generate-signature':
            try:
                # 读取请求体
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                
                # 解析JSON数据
                try:
                    data = json.loads(post_data.decode('utf-8'))
                except json.JSONDecodeError:
                    self._set_headers(400)
                    response = {"success": False, "error": "无效的JSON格式"}
                    self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                    return
                
                # 验证必需参数
                required_fields = ['method', 'uri', 'a1_value']
                for field in required_fields:
                    if field not in data:
                        self._set_headers(400)
                        response = {"success": False, "error": f"缺少必需参数: {field}"}
                        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                        return
                
                # 验证method参数
                if data['method'].upper() not in ['GET', 'POST']:
                    self._set_headers(400)
                    response = {"success": False, "error": "method必须是GET或POST"}
                    self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                    return
                
                # 验证uri参数
                if not data['uri'].startswith('/'):
                    self._set_headers(400)
                    response = {"success": False, "error": "uri必须以/开头"}
                    self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                    return
                
                # 验证a1_value参数
                if not data['a1_value'].strip():
                    self._set_headers(400)
                    response = {"success": False, "error": "a1_value不能为空"}
                    self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                    return
                
                # 获取payload参数（可选）
                payload = data.get('payload', {})
                
                # 调用xhshow生成签名
                signature = xhshow_client.sign_xs(
                    method=data['method'].upper(),
                    uri=data['uri'],
                    a1_value=data['a1_value'].strip(),
                    payload=payload
                )
                
                # 返回成功响应
                self._set_headers()
                response = {
                    "success": True,
                    "signature": signature,
                    "error": None
                }
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                
            except Exception as e:
                # 返回错误响应
                self._set_headers(500)
                response = {
                    "success": False,
                    "signature": None,
                    "error": f"签名生成失败: {str(e)}"
                }
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        else:
            self._set_headers(404)
            response = {"error": "Not Found"}
            self.wfile.write(json.dumps(response).encode('utf-8'))

    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def run_server(port=8000):
    """启动服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, SignatureHandler)
    
    print(f"正在启动小红书签名生成服务...")
    print(f"服务地址: http://localhost:{port}")
    print(f"健康检查: http://localhost:{port}/health")
    print(f"API信息: http://localhost:{port}/api-info")
    print("按 Ctrl+C 停止服务")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n正在停止服务...")
        httpd.server_close()
        print("服务已停止")

if __name__ == '__main__':
    run_server()
