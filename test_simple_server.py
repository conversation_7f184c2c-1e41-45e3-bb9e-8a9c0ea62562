# -*- coding: utf-8 -*-
"""
简化版服务器测试客户端
使用Python内置的urllib，不依赖外部库
"""

import json
import urllib.request
import urllib.parse

# 服务地址
BASE_URL = "http://localhost:8000"

def make_request(url, data=None, method='GET'):
    """发送HTTP请求"""
    try:
        if data:
            # POST请求
            data_bytes = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(url, data=data_bytes)
            req.add_header('Content-Type', 'application/json')
        else:
            # GET请求
            req = urllib.request.Request(url)
        
        with urllib.request.urlopen(req) as response:
            response_data = response.read().decode('utf-8')
            return response.status, json.loads(response_data)
    except urllib.error.HTTPError as e:
        error_data = e.read().decode('utf-8')
        try:
            return e.code, json.loads(error_data)
        except:
            return e.code, {"error": error_data}
    except Exception as e:
        return None, {"error": str(e)}

def test_health():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    status, response = make_request(f"{BASE_URL}/health")
    print(f"状态码: {status}")
    print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    print()

def test_api_info():
    """测试API信息接口"""
    print("=== 测试API信息接口 ===")
    status, response = make_request(f"{BASE_URL}/api-info")
    print(f"状态码: {status}")
    print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    print()

def test_generate_signature():
    """测试签名生成接口"""
    print("=== 测试签名生成接口 ===")
    
    # 测试数据
    test_data = {
        "method": "GET",
        "uri": "/api/sns/web/v1/user_posted",
        "a1_value": "test_a1_cookie_value",
        "payload": {
            "num": "30",
            "cursor": "",
            "user_id": "123"
        }
    }
    
    status, response = make_request(f"{BASE_URL}/generate-signature", test_data)
    print(f"状态码: {status}")
    print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    print()

def test_invalid_method():
    """测试无效的HTTP方法"""
    print("=== 测试无效的HTTP方法 ===")
    
    test_data = {
        "method": "DELETE",  # 无效方法
        "uri": "/api/sns/web/v1/user_posted",
        "a1_value": "test_a1_cookie_value",
        "payload": {}
    }
    
    status, response = make_request(f"{BASE_URL}/generate-signature", test_data)
    print(f"状态码: {status}")
    print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    print()

def test_missing_parameter():
    """测试缺少必需参数"""
    print("=== 测试缺少必需参数 ===")
    
    test_data = {
        "method": "GET",
        "uri": "/api/sns/web/v1/user_posted"
        # 缺少a1_value参数
    }
    
    status, response = make_request(f"{BASE_URL}/generate-signature", test_data)
    print(f"状态码: {status}")
    print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    print()

def test_root_endpoint():
    """测试根路径"""
    print("=== 测试根路径 ===")
    status, response = make_request(f"{BASE_URL}/")
    print(f"状态码: {status}")
    print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
    print()

if __name__ == "__main__":
    print("小红书签名生成服务测试客户端（简化版）")
    print("请确保服务已启动 (python3 simple_server.py)")
    print("=" * 60)
    
    # 运行测试
    test_root_endpoint()
    test_health()
    test_api_info()
    test_generate_signature()
    test_invalid_method()
    test_missing_parameter()
    
    print("测试完成！")
    print("\n使用说明:")
    print("1. 启动服务: python3 simple_server.py")
    print("2. 健康检查: http://localhost:8000/health")
    print("3. API信息: http://localhost:8000/api-info")
    print("4. 运行测试: python3 test_simple_server.py")
